using System;
using System.IO;
using System.Reflection;

// 模擬 ProjectPathHelper 邏輯
public static class TestProjectPathHelper
{
    private static string? _projectRoot;
    
    public static string ProjectRoot
    {
        get
        {
            if (_projectRoot != null)
                return _projectRoot;
                
            _projectRoot = FindProjectRoot();
            return _projectRoot;
        }
    }
    
    private static string FindProjectRoot()
    {
        var currentDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
        
        if (currentDirectory == null)
        {
            currentDirectory = Directory.GetCurrentDirectory();
        }
        
        var directory = new DirectoryInfo(currentDirectory);
        
        while (directory != null)
        {
            if (directory.GetFiles("*.sln").Length > 0)
            {
                return directory.FullName;
            }
            
            if (directory.GetDirectories("src").Length > 0)
            {
                return directory.FullName;
            }
            
            directory = directory.Parent;
        }
        
        return Directory.GetCurrentDirectory();
    }
    
    public static string ResolveProjectPath(string relativePath)
    {
        if (Path.IsPathRooted(relativePath))
        {
            return relativePath;
        }
        
        return Path.GetFullPath(Path.Combine(ProjectRoot, relativePath));
    }
    
    public static string LogsDirectory => ResolveProjectPath("data/logs");
}

class Program
{
    static void Main()
    {
        Console.WriteLine($"當前工作目錄: {Directory.GetCurrentDirectory()}");
        Console.WriteLine($"執行檔案位置: {Assembly.GetExecutingAssembly().Location}");
        Console.WriteLine($"專案根目錄: {TestProjectPathHelper.ProjectRoot}");
        Console.WriteLine($"Log 目錄路徑: {TestProjectPathHelper.LogsDirectory}");
        Console.WriteLine($"舊方法路徑: {Path.GetFullPath("./data/logs")}");
    }
}