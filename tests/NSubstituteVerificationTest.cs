using NSubstitute;
using Xunit;
using System.IO.Abstractions;

namespace VideoContentAnalyzer.Tests
{
    public class NSubstituteVerificationTest
    {
        [Fact]
        public void NSubstitute_ShouldWork_WithBasicMocking()
        {
            // Arrange
            var fileSystem = Substitute.For<IFileSystem>();
            fileSystem.File.Exists("test.txt").Returns(true);

            // Act
            var result = fileSystem.File.Exists("test.txt");

            // Assert
            Assert.True(result);
            fileSystem.File.Received(1).Exists("test.txt");
        }
    }
}
