<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <OutputType>Exe</OutputType>
    <StartupObject>VideoContentAnalyzer.Tests.IntegrationTestProgram</StartupObject>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="NSubstitute" Version="5.3.0" />
    <PackageReference Include="xunit" Version="2.4.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5" />
    <PackageReference Include="Moq" Version="4.20.69" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="Microsoft.Extensions.FileSystemGlobbing" Version="8.0.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="System.IO.Abstractions" Version="21.1.3" />
    <PackageReference Include="System.IO.Abstractions.TestingHelpers" Version="21.1.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../src/VideoContentAnalyzer.Core/VideoContentAnalyzer.Core.csproj" />
    <ProjectReference Include="../src/VideoContentAnalyzer.Infrastructure/VideoContentAnalyzer.Infrastructure.csproj" />
  </ItemGroup>

</Project>
