using FluentAssertions;
using VideoContentAnalyzer.Core.Models;

namespace VideoContentAnalyzer.Tests.VideoContentAnalyzer.UnitTests.TestHelpers;

/// <summary>
/// 提供自訂的斷言輔助方法
/// </summary>
public static class AssertionHelpers
{
    /// <summary>
    /// 驗證 SceneDescription 物件的基本屬性
    /// </summary>
    public static void ShouldBeValidSceneDescription(this SceneDescription sceneDescription)
    {
        sceneDescription.Should().NotBeNull();
        sceneDescription.MainDescription.Should().NotBeNullOrEmpty();
    }

    /// <summary>
    /// 驗證 SceneDescription 包含餐廳相關資訊
    /// </summary>
    public static void ShouldContainRestaurantInfo(this SceneDescription sceneDescription)
    {
        sceneDescription.ShouldBeValidSceneDescription();
        sceneDescription.RestaurantCategory.Should().NotBeNullOrEmpty();
        sceneDescription.RestaurantCategory.Should().NotBe("非餐廳");
    }

    /// <summary>
    /// 驗證 PlaceInfo 物件的基本屬性
    /// </summary>
    public static void ShouldBeValidPlaceInfo(this PlaceInfo placeInfo)
    {
        placeInfo.Should().NotBeNull();
        placeInfo.Name.Should().NotBeNullOrEmpty();
        placeInfo.Confidence.Should().BeInRange(0.0, 1.0);
    }

    /// <summary>
    /// 驗證 PlaceInfo 包含完整的地址資訊
    /// </summary>
    public static void ShouldHaveCompleteAddressInfo(this PlaceInfo placeInfo)
    {
        placeInfo.ShouldBeValidPlaceInfo();
        placeInfo.Address.Should().NotBeNullOrEmpty();
    }

    /// <summary>
    /// 驗證 DetectedText 物件的基本屬性
    /// </summary>
    public static void ShouldBeValidDetectedText(this DetectedText detectedText)
    {
        detectedText.Should().NotBeNull();
        detectedText.Text.Should().NotBeNullOrEmpty();
        detectedText.Confidence.Should().BeInRange(0.0, 1.0);
        detectedText.Language.Should().NotBeNullOrEmpty();
    }

    /// <summary>
    /// 驗證 SubtitleSegment 物件的基本屬性
    /// </summary>
    public static void ShouldBeValidSubtitleSegment(this SubtitleSegment subtitleSegment)
    {
        subtitleSegment.Should().NotBeNull();
        subtitleSegment.Text.Should().NotBeNullOrEmpty();
        subtitleSegment.StartTime.Should().BeLessThan(subtitleSegment.EndTime);
        subtitleSegment.Confidence.Should().BeInRange(0.0, 1.0);
    }

    /// <summary>
    /// 驗證 SubtitleAnalysis 物件的基本屬性
    /// </summary>
    public static void ShouldBeValidSubtitleAnalysis(this SubtitleAnalysis subtitleAnalysis)
    {
        subtitleAnalysis.Should().NotBeNull();
        subtitleAnalysis.Sentiment.Should().NotBeNullOrEmpty();
        subtitleAnalysis.Topics.Should().NotBeNull();
        subtitleAnalysis.Keywords.Should().NotBeNull();
    }

    /// <summary>
    /// 驗證 VideoAnalysisResult 物件的基本屬性
    /// </summary>
    public static void ShouldBeValidVideoAnalysisResult(this VideoAnalysisResult result)
    {
        result.Should().NotBeNull();
        result.VideoPath.Should().NotBeNullOrEmpty();
        result.VideoDuration.Should().BePositive();
        result.AnalysisTimestamp.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
        result.FrameAnalyses.Should().NotBeNull();
        result.SubtitleSegments.Should().NotBeNull();
        result.Summary.Should().NotBeNull();
        result.PerformanceMetrics.Should().NotBeNull();
    }

    /// <summary>
    /// 驗證 FrameAnalysis 物件的基本屬性
    /// </summary>
    public static void ShouldBeValidFrameAnalysis(this FrameAnalysis frameAnalysis)
    {
        frameAnalysis.Should().NotBeNull();
        frameAnalysis.FramePath.Should().NotBeNullOrEmpty();
        frameAnalysis.Timestamp.Should().BeGreaterOrEqualTo(TimeSpan.Zero);
        frameAnalysis.Scene.Should().NotBeNull();
        frameAnalysis.ConfidenceScore.Should().BeInRange(0.0, 1.0);
        frameAnalysis.DetectedTexts.Should().NotBeNull();
        frameAnalysis.PlaceInfos.Should().NotBeNull();
    }

    /// <summary>
    /// 驗證 YouTubeVideoInfo 物件的基本屬性
    /// </summary>
    public static void ShouldBeValidYouTubeVideoInfo(this YouTubeVideoInfo videoInfo)
    {
        videoInfo.Should().NotBeNull();
        videoInfo.Id.Should().NotBeNullOrEmpty();
        videoInfo.Title.Should().NotBeNullOrEmpty();
        videoInfo.Url.Should().NotBeNullOrEmpty();
        videoInfo.Channel.Should().NotBeNullOrEmpty();
    }

    /// <summary>
    /// 驗證 YouTubeDownloadResult 的成功狀態
    /// </summary>
    public static void ShouldBeSuccessfulDownload(this YouTubeDownloadResult result)
    {
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.VideoPath.Should().NotBeNullOrEmpty();
        result.ErrorMessage.Should().BeNullOrEmpty();
        result.VideoInfo.Should().NotBeNull();
    }

    /// <summary>
    /// 驗證 YouTubeDownloadResult 的失敗狀態
    /// </summary>
    public static void ShouldBeFailedDownload(this YouTubeDownloadResult result)
    {
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.ErrorMessage.Should().NotBeNullOrEmpty();
    }

    /// <summary>
    /// 驗證集合不為空且所有元素都有效
    /// </summary>
    public static void ShouldAllBeValid<T>(this IEnumerable<T> collection, Action<T> validator)
    {
        collection.Should().NotBeNull();
        collection.Should().NotBeEmpty();
        
        foreach (var item in collection)
        {
            validator(item);
        }
    }

    /// <summary>
    /// 驗證時間範圍的合理性
    /// </summary>
    public static void ShouldBeReasonableTimeSpan(this TimeSpan timeSpan, TimeSpan? minimum = null, TimeSpan? maximum = null)
    {
        timeSpan.Should().BeGreaterOrEqualTo(minimum ?? TimeSpan.Zero);
        
        if (maximum.HasValue)
        {
            timeSpan.Should().BeLessOrEqualTo(maximum.Value);
        }
    }

    /// <summary>
    /// 驗證信心度分數的合理性
    /// </summary>
    public static void ShouldBeValidConfidenceScore(this double confidence)
    {
        confidence.Should().BeInRange(0.0, 1.0);
    }

    /// <summary>
    /// 驗證檔案路徑的格式
    /// </summary>
    public static void ShouldBeValidFilePath(this string filePath)
    {
        filePath.Should().NotBeNullOrEmpty();
        filePath.Should().NotContain("\\\\"); // 避免雙反斜線
        filePath.Should().NotEndWith("/").And.NotEndWith("\\"); // 檔案路徑不應以分隔符結尾
    }

    /// <summary>
    /// 驗證 URL 的格式
    /// </summary>
    public static void ShouldBeValidUrl(this string url)
    {
        url.Should().NotBeNullOrEmpty();
        (url.StartsWith("http://") || url.StartsWith("https://")).Should().BeTrue();
        Uri.TryCreate(url, UriKind.Absolute, out _).Should().BeTrue();
    }
}
