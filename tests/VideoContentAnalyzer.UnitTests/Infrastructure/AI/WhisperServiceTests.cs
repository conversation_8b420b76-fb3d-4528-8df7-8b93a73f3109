using System.IO.Abstractions.TestingHelpers;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using VideoContentAnalyzer.Infrastructure.AI;
using VideoContentAnalyzer.Tests.VideoContentAnalyzer.UnitTests.TestHelpers;
using Xunit;

namespace VideoContentAnalyzer.Tests.VideoContentAnalyzer.UnitTests.Infrastructure.AI;

/// <summary>
/// WhisperService 的單元測試
/// 注意：這些測試需要重構 WhisperService 以支持 IFileSystem 依賴注入
/// 目前 WhisperService 直接使用文件系統，因此這些更像是整合測試
/// </summary>
public class WhisperServiceTests
{
    private readonly ILogger<WhisperService> _mockLogger;
    private readonly IOptions<WhisperOptions> _options;
    private readonly MockFileSystem _fileSystem;
    private readonly WhisperService _service;

    public WhisperServiceTests()
    {
        _mockLogger = TestFixtures.CreateMockLogger<WhisperService>();
        _options = TestFixtures.CreateMockWhisperOptions();
        _fileSystem = TestFixtures.CreateMockFileSystem();

        // 現在 WhisperService 支援 IFileSystem 依賴注入
        _service = new WhisperService(_options, _mockLogger, _fileSystem);
    }

    #region TranscribeAudioAsync Tests

    [Fact]
    public async Task TranscribeAudioAsync_WithValidAudioFile_ShouldReturnSubtitleSegments()
    {
        // Arrange
        var audioPath = "/test/audio.wav";
        _fileSystem.AddFile(audioPath, "fake audio data");

        // Act & Assert
        // 由於 Whisper.net 需要實際的音頻文件和模型，這個測試會拋出異常
        // 但我們可以驗證文件存在性檢查是否正常工作
        var exception = await Assert.ThrowsAsync<Exception>(() => _service.TranscribeAudioAsync(audioPath));

        // 驗證不是因為文件不存在而失敗
        exception.Should().NotBeOfType<FileNotFoundException>();

        TestFixtures.VerifyLoggerCalledWithMessage(_mockLogger, LogLevel.Information, "Starting audio transcription");
    }

    [Fact]
    public async Task TranscribeAudioAsync_WithNonExistentFile_ShouldThrowFileNotFoundException()
    {
        // Arrange
        var nonExistentPath = "/nonexistent/audio.wav";

        // Act & Assert
        await Assert.ThrowsAsync<FileNotFoundException>(
            () => _service.TranscribeAudioAsync(nonExistentPath));
    }

    [Fact]
    public async Task TranscribeAudioAsync_WhenWhisperNetFails_ShouldFallbackToCLI()
    {
        // Arrange
        var audioPath = "/test/audio.wav";
        _fileSystem.AddFile(audioPath, "fake audio data");

        // Act & Assert
        // 由於沒有實際的 Whisper 模型，這個測試會拋出異常
        // 但我們可以驗證文件存在性檢查正常工作
        var exception = await Assert.ThrowsAsync<Exception>(() => _service.TranscribeAudioAsync(audioPath));

        // 驗證不是因為文件不存在而失敗
        exception.Should().NotBeOfType<FileNotFoundException>();
    }

    [Fact]
    public async Task TranscribeAudioAsync_WhenBothMethodsFail_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var audioPath = "/test/audio.wav";
        _fileSystem.AddFile(audioPath, "fake audio data");

        // Act & Assert
        // 由於沒有實際的 Whisper 模型和 CLI，這個測試會拋出異常
        var exception = await Assert.ThrowsAsync<Exception>(() => _service.TranscribeAudioAsync(audioPath));

        // 驗證不是因為文件不存在而失敗
        exception.Should().NotBeOfType<FileNotFoundException>();
    }

    [Fact]
    public async Task TranscribeAudioAsync_WithCancellationToken_ShouldRespectCancellation()
    {
        // Arrange
        var audioPath = "/test/audio.wav";
        _fileSystem.AddFile(audioPath, "fake audio data");
        var cancellationToken = TestFixtures.CreateCancelledToken();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => _service.TranscribeAudioAsync(audioPath, cancellationToken));
    }

    [Fact(Skip = "需要實際的 FFmpeg 和 Whisper 環境")]
    public async Task TranscribeAudioAsync_WithLargeAudioFile_ShouldHandleEfficiently()
    {
        // Arrange
        var audioPath = "/test/large_audio.wav";
        var largeAudioData = new string('x', 10_000_000); // 10MB 的假資料
        _fileSystem.File.WriteAllText(audioPath, largeAudioData);

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var result = await _service.TranscribeAudioAsync(audioPath);
        stopwatch.Stop();

        // Assert
        result.Should().NotBeNull();
        // 驗證處理大檔案的效率（實際時間取決於硬體）
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(60000); // 1分鐘內完成
    }

    [Fact(Skip = "需要實際的 FFmpeg 和 Whisper 環境")]
    public async Task TranscribeAudioAsync_WithDifferentLanguages_ShouldDetectCorrectly()
    {
        // Arrange
        var audioPath = "/test/multilingual_audio.wav";
        _fileSystem.File.WriteAllText(audioPath, "fake multilingual audio data");

        // 模擬多語言音訊檔案

        // Act
        var result = await _service.TranscribeAudioAsync(audioPath);

        // Assert
        result.Should().NotBeNull();
        result.Should().NotBeEmpty();

        // 驗證語言檢測
        result.Should().AllSatisfy(segment =>
        {
            segment.Language.Should().NotBeNullOrEmpty();
            segment.Language.Should().BeOneOf("zh-TW", "ja", "ko", "en", "auto");
        });
    }

    #endregion

    #region ExtractAudioFromVideoAsync Tests

    [Fact]
    public async Task ExtractAudioFromVideoAsync_WithValidVideoFile_ShouldReturnAudioPath()
    {
        // Arrange
        var videoPath = "/test/video.mp4";
        _fileSystem.AddFile(videoPath, "fake video data");

        // Act & Assert
        // 由於沒有實際的 FFmpeg，這個測試會拋出異常
        // 但我們可以驗證文件存在性檢查正常工作
        var exception = await Assert.ThrowsAsync<Exception>(() => _service.ExtractAudioFromVideoAsync(videoPath));

        // 驗證不是因為文件不存在而失敗
        exception.Should().NotBeOfType<FileNotFoundException>();
    }

    [Fact]
    public async Task ExtractAudioFromVideoAsync_WithNonExistentVideo_ShouldThrowFileNotFoundException()
    {
        // Arrange
        var nonExistentPath = "/nonexistent/video.mp4";

        // Act & Assert
        await Assert.ThrowsAsync<FileNotFoundException>(
            () => _service.ExtractAudioFromVideoAsync(nonExistentPath));
    }

    [Fact(Skip = "需要實際的 FFmpeg 和 Whisper 環境")]
    public async Task ExtractAudioFromVideoAsync_WithInvalidVideoFormat_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var invalidVideoPath = "/test/invalid.txt";
        _fileSystem.File.WriteAllText(invalidVideoPath, "not a video file");

        // Act & Assert
        // 實際測試需要模擬 FFmpeg 錯誤
        // await Assert.ThrowsAsync<InvalidOperationException>(
        //     () => _service.ExtractAudioFromVideoAsync(invalidVideoPath));

        await Task.CompletedTask; // 暫時跳過實際執行
    }

    [Fact(Skip = "需要實際的 FFmpeg 和 Whisper 環境")]
    public async Task ExtractAudioFromVideoAsync_WithCancellationToken_ShouldRespectCancellation()
    {
        // Arrange
        var videoPath = "/test/video.mp4";
        _fileSystem.File.WriteAllText(videoPath, "fake video data");
        var cancellationToken = TestFixtures.CreateCancelledToken();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => _service.ExtractAudioFromVideoAsync(videoPath, cancellationToken));
    }

    [Fact(Skip = "需要實際的 FFmpeg 和 Whisper 環境")]
    public async Task ExtractAudioFromVideoAsync_WithCustomOutputPath_ShouldUseSpecifiedPath()
    {
        // Arrange
        var videoPath = "/test/video.mp4";
        var customOutputPath = "/test/custom_audio.wav";
        _fileSystem.File.WriteAllText(videoPath, "fake video data");

        // Act
        // 假設 ExtractAudioFromVideoAsync 支援自訂輸出路徑
        var result = await _service.ExtractAudioFromVideoAsync(videoPath);

        // Assert
        result.Should().NotBeNullOrEmpty();
        result.ShouldBeValidFilePath();
    }

    #endregion

    #region TranscribeUsingDotNetAsync Tests (Private Method Testing)

    // 注意：這些是私有方法的測試，通常透過公開方法間接測試
    // 如果需要直接測試私有方法，可以使用反射或將方法設為 internal 並使用 InternalsVisibleTo

    [Fact(Skip = "需要實際的 FFmpeg 和 Whisper 環境")]
    public async Task TranscribeUsingDotNetAsync_WithValidAudio_ShouldReturnSegments()
    {
        // Arrange
        var audioPath = "/test/audio.wav";
        _fileSystem.File.WriteAllText(audioPath, "fake audio data");

        // Act
        // 透過公開方法間接測試
        var result = await _service.TranscribeAudioAsync(audioPath);

        // Assert
        result.Should().NotBeNull();
        // 驗證使用了 Whisper.net 方法
        TestFixtures.VerifyLoggerCalledWithMessage(_mockLogger, LogLevel.Information, "Starting audio transcription");
    }

    #endregion

    #region TranscribeUsingCliAsync Tests (Private Method Testing)

    [Fact(Skip = "需要實際的 FFmpeg 和 Whisper 環境")]
    public async Task TranscribeUsingCliAsync_WithValidAudio_ShouldReturnSegments()
    {
        // Arrange
        var audioPath = "/test/audio.wav";
        _fileSystem.File.WriteAllText(audioPath, "fake audio data");

        // 模擬 Whisper.net 失敗，強制使用 CLI

        // Act
        // 透過公開方法間接測試
        // 實際測試需要模擬 Whisper.net 失敗的情況

        await Task.CompletedTask; // 暫時跳過實際執行
    }

    [Fact(Skip = "需要實際的 FFmpeg 和 Whisper 環境")]
    public async Task TranscribeUsingCliAsync_WithMissingWhisperCLI_ShouldThrowException()
    {
        // Arrange
        var audioPath = "/test/audio.wav";
        _fileSystem.File.WriteAllText(audioPath, "fake audio data");

        // 模擬系統中沒有安裝 Whisper CLI

        // Act & Assert
        // 實際測試需要模擬 CLI 不存在的情況
        await Task.CompletedTask; // 暫時跳過實際執行
    }

    #endregion

    #region Performance Tests

    [Fact(Skip = "需要實際的 FFmpeg 和 Whisper 環境")]
    public async Task TranscribeAudioAsync_WithMultipleFiles_ShouldHandleConcurrently()
    {
        // Arrange
        var audioPaths = new[]
        {
            "/test/audio1.wav",
            "/test/audio2.wav",
            "/test/audio3.wav"
        };

        foreach (var path in audioPaths)
        {
            _fileSystem.File.WriteAllText(path, "fake audio data");
        }

        // Act
        var tasks = audioPaths.Select(path => _service.TranscribeAudioAsync(path));
        var results = await Task.WhenAll(tasks);

        // Assert
        results.Should().HaveCount(3);
        results.Should().AllSatisfy(result =>
        {
            result.Should().NotBeNull();
            result.Should().NotBeEmpty();
        });
    }

    [Fact(Skip = "需要實際的 FFmpeg 和 Whisper 環境")]
    public async Task TranscribeAudioAsync_WithLongAudio_ShouldProvideProgressUpdates()
    {
        // Arrange
        var audioPath = "/test/long_audio.wav";
        _fileSystem.File.WriteAllText(audioPath, "fake long audio data");

        // 模擬長音訊檔案的轉錄

        // Act & Assert
        // 實際測試需要驗證進度更新機制
        await Task.CompletedTask; // 暫時跳過實際執行
    }

    #endregion

    #region Configuration Tests

    [Fact(Skip = "需要實際的 FFmpeg 和 Whisper 環境")]
    public async Task TranscribeAudioAsync_WithDifferentModelSizes_ShouldUseCorrectModel()
    {
        // Arrange
        var audioPath = "/test/audio.wav";
        _fileSystem.File.WriteAllText(audioPath, "fake audio data");

        // 測試不同的模型大小設定
        var modelSizes = new[] { "tiny", "base", "small", "medium", "large" };

        foreach (var modelSize in modelSizes)
        {
            // 更新選項中的模型大小
            var options = TestFixtures.CreateMockWhisperOptions();
            options.Value.ModelSize = modelSize;

            // Act
            var result = await _service.TranscribeAudioAsync(audioPath);

            // Assert
            result.Should().NotBeNull();
            // 驗證使用了正確的模型大小
        }
    }

    [Fact(Skip = "需要實際的 FFmpeg 和 Whisper 環境")]
    public async Task TranscribeAudioAsync_WithCustomLanguage_ShouldUseSpecifiedLanguage()
    {
        // Arrange
        var audioPath = "/test/audio.wav";
        _fileSystem.File.WriteAllText(audioPath, "fake audio data");

        // 設定特定語言
        var options = TestFixtures.CreateMockWhisperOptions();
        options.Value.Language = "ja"; // 日文

        // Act
        var result = await _service.TranscribeAudioAsync(audioPath);

        // Assert
        result.Should().NotBeNull();
        result.Should().AllSatisfy(segment =>
        {
            segment.Language.Should().Be("ja");
        });
    }

    #endregion
}
