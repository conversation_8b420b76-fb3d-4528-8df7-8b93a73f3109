using System.IO;
using System.Net;
using System.Text.Json;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using RestSharp;
using VideoContentAnalyzer.Infrastructure.AI;
using VideoContentAnalyzer.Infrastructure.Http;
using VideoContentAnalyzer.Tests.VideoContentAnalyzer.UnitTests.TestHelpers;
using Xunit;

namespace VideoContentAnalyzer.Tests.VideoContentAnalyzer.UnitTests.Infrastructure.AI;

/// <summary>
/// LMStudioClient 的整合測試
/// 注意：由於 LMStudioClient 直接創建 RestClient，這些測試更像是整合測試
/// 未來應該重構 LMStudioClient 以支持依賴注入來實現真正的單元測試
/// </summary>
public class LMStudioClientTests : IDisposable
{
    private readonly ILogger<LMStudioClient> _mockLogger;
    private readonly IOptions<LMStudioOptions> _options;
    private readonly IRestClientFactory _mockRestClientFactory;
    private readonly LMStudioClient _client;

    public LMStudioClientTests()
    {
        _mockLogger = TestFixtures.CreateMockLogger<LMStudioClient>();
        _options = TestFixtures.CreateMockLMStudioOptions();
        _mockRestClientFactory = TestFixtures.CreateMockRestClientFactory();

        // 創建 LMStudioClient 實例，現在使用 Mock IRestClientFactory
        _client = new LMStudioClient(_options, _mockLogger, _mockRestClientFactory);
    }

    #region SendVisionRequestAsync Tests

    [Fact(Skip = "需要檔案系統重構以支援單元測試")]
    public async Task SendVisionRequestAsync_WithValidImageAndPrompt_ShouldReturnResponse()
    {
        // Arrange
        var imagePath = "/test/frame.jpg";
        var prompt = "分析這張圖片";
        var expectedResponse = """
        {
            "choices": [
                {
                    "message": {
                        "content": "這是一家日式拉麵店的外觀"
                    }
                }
            ]
        }
        """;

        // 建立模擬的檔案系統
        var fileSystem = TestFixtures.CreateMockFileSystem();
        fileSystem.File.Exists(imagePath).Returns(true);

        // 模擬成功的 HTTP 回應
        var mockResponse = TestFixtures.CreateSuccessfulRestResponse(expectedResponse);

        // 模擬 RestClient 和 RestClientFactory
        var mockRestClient = Substitute.For<global::VideoContentAnalyzer.Infrastructure.Http.IRestClient>();
        mockRestClient.ExecuteAsync(Arg.Any<RestRequest>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(mockResponse));

        var mockRestClientFactory = Substitute.For<IRestClientFactory>();
        mockRestClientFactory.CreateClient(Arg.Any<RestClientOptions>())
            .Returns(mockRestClient);

        // 建立 LMStudioClient
        var options = Microsoft.Extensions.Options.Options.Create(MockData.CreateLMStudioOptions());
        var logger = Substitute.For<ILogger<LMStudioClient>>();
        var client = new LMStudioClient(options, logger, mockRestClientFactory);

        // Act
        var (responseText, duration) = await client.SendVisionRequestAsync(imagePath, prompt);

        // Assert
        responseText.Should().NotBeNull();
        responseText.Should().Contain("這是一家日式拉麵店的外觀");
        duration.Should().BePositive();
        await Task.CompletedTask;

        // 預期的斷言：
        // var (result, duration) = await _client.SendVisionRequestAsync(imagePath, prompt);
        // result.Should().Contain("日式拉麵店");
        // duration.Should().BePositive();
    }

    [Fact()]
    public async Task SendVisionRequestAsync_WithInvalidImagePath_ShouldThrowFileNotFoundException()
    {
        // Arrange
        var invalidImagePath = "/nonexistent/image.jpg";
        var prompt = "分析這張圖片";

        // Act & Assert
        // 在某些系統上，不存在的路徑可能拋出 DirectoryNotFoundException 而不是 FileNotFoundException
        await Assert.ThrowsAnyAsync<IOException>(
            () => _client.SendVisionRequestAsync(invalidImagePath, prompt));
    }

    [Fact(Skip = "需要檔案系統重構以支援單元測試")]
    public async Task SendVisionRequestAsync_WithHttpError_ShouldThrowHttpRequestException()
    {
        // Arrange
        var imagePath = "/test/frame.jpg";
        var prompt = "分析這張圖片";

        // 建立模擬的檔案系統
        var fileSystem = TestFixtures.CreateMockFileSystem();
        fileSystem.File.Exists(imagePath).Returns(true);

        // 模擬 HTTP 錯誤回應
        var mockResponse = TestFixtures.CreateFailedRestResponse(HttpStatusCode.InternalServerError, "Internal Server Error");

        // 模擬 RestClient 和 RestClientFactory
        var mockRestClient = Substitute.For<global::VideoContentAnalyzer.Infrastructure.Http.IRestClient>();
        mockRestClient.ExecuteAsync(Arg.Any<RestRequest>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(mockResponse));

        var mockRestClientFactory = Substitute.For<IRestClientFactory>();
        mockRestClientFactory.CreateClient(Arg.Any<RestClientOptions>())
            .Returns(mockRestClient);

        // 建立 LMStudioClient
        var options = Microsoft.Extensions.Options.Options.Create(MockData.CreateLMStudioOptions());
        var logger = Substitute.For<ILogger<LMStudioClient>>();
        var client = new LMStudioClient(options, logger, mockRestClientFactory);

        // Act & Assert
        await Assert.ThrowsAsync<HttpRequestException>(
            () => client.SendVisionRequestAsync(imagePath, prompt));
    }

    [Fact(Skip = "需要檔案系統重構以支援單元測試")]
    public async Task SendVisionRequestAsync_WithCancellationToken_ShouldRespectCancellation()
    {
        // Arrange
        var imagePath = "/test/frame.jpg";
        var prompt = "分析這張圖片";
        var cancellationToken = TestFixtures.CreateCancelledToken();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => _client.SendVisionRequestAsync(imagePath, prompt, cancellationToken));
    }

    [Fact(Skip = "需要檔案系統重構以支援單元測試")]
    public async Task SendVisionRequestAsync_WithTimeout_ShouldThrowTimeoutException()
    {
        // Arrange
        var imagePath = "/test/frame.jpg";
        var prompt = "分析這張圖片";
        var shortTimeoutToken = TestFixtures.CreateTimeoutToken(100); // 100ms timeout

        // 建立模擬的檔案系統
        var fileSystem = TestFixtures.CreateMockFileSystem();
        fileSystem.File.Exists(imagePath).Returns(true);

        // 模擬延遲回應
        var mockRestClient = Substitute.For<global::VideoContentAnalyzer.Infrastructure.Http.IRestClient>();
        mockRestClient.ExecuteAsync(Arg.Any<RestRequest>(), Arg.Any<CancellationToken>())
            .Returns(async (callInfo) =>
            {
                var token = callInfo.ArgAt<CancellationToken>(1);
                await Task.Delay(200, token); // 延遲 200ms，超過 100ms 超時
                return TestFixtures.CreateSuccessfulRestResponse("{}");
            });

        var mockRestClientFactory = Substitute.For<IRestClientFactory>();
        mockRestClientFactory.CreateClient(Arg.Any<RestClientOptions>())
            .Returns(mockRestClient);

        // 建立 LMStudioClient
        var options = Microsoft.Extensions.Options.Options.Create(MockData.CreateLMStudioOptions());
        var logger = Substitute.For<ILogger<LMStudioClient>>();
        var client = new LMStudioClient(options, logger, mockRestClientFactory);

        // Act & Assert
        await Assert.ThrowsAnyAsync<OperationCanceledException>(
            () => client.SendVisionRequestAsync(imagePath, prompt, shortTimeoutToken));
    }

    [Fact(Skip = "需要檔案系統重構以支援單元測試")]
    public async Task SendVisionRequestAsync_WithInvalidJsonResponse_ShouldThrowJsonException()
    {
        // Arrange
        var imagePath = "/test/frame.jpg";
        var prompt = "分析這張圖片";
        var invalidJsonResponse = "{ invalid json }";

        // 建立模擬的檔案系統
        var fileSystem = TestFixtures.CreateMockFileSystem();
        fileSystem.File.Exists(imagePath).Returns(true);

        // 模擬無效 JSON 回應
        var mockResponse = TestFixtures.CreateSuccessfulRestResponse(invalidJsonResponse);

        var mockRestClient = Substitute.For<global::VideoContentAnalyzer.Infrastructure.Http.IRestClient>();
        mockRestClient.ExecuteAsync(Arg.Any<RestRequest>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(mockResponse));

        var mockRestClientFactory = Substitute.For<IRestClientFactory>();
        mockRestClientFactory.CreateClient(Arg.Any<RestClientOptions>())
            .Returns(mockRestClient);

        // 建立 LMStudioClient
        var options = Microsoft.Extensions.Options.Options.Create(MockData.CreateLMStudioOptions());
        var logger = Substitute.For<ILogger<LMStudioClient>>();
        var client = new LMStudioClient(options, logger, mockRestClientFactory);

        // Act & Assert
        await Assert.ThrowsAsync<JsonException>(
            () => client.SendVisionRequestAsync(imagePath, prompt));
    }

    #endregion

    #region SendTextRequestAsync Tests

    [Fact(Skip = "RestSharp 的 IsSuccessful 屬性無法在單元測試中正確模擬，需要重構為整合測試")]
    public async Task SendTextRequestAsync_WithValidPrompt_ShouldReturnResponse()
    {
        // Arrange
        var prompt = "請分析以下文字內容";
        var expectedResponse = """
        {
            "choices": [
                {
                    "message": {
                        "content": "這是分析結果"
                    }
                }
            ]
        }
        """;

        // 使用 TestFixtures 創建成功的 RestResponse，但手動確保 IsSuccessful 為 true
        var mockResponse = TestFixtures.CreateSuccessfulRestResponse(expectedResponse);

        // 創建 mock RestClient 和 RestClientFactory
        var mockRestClient = Substitute.For<global::VideoContentAnalyzer.Infrastructure.Http.IRestClient>();
        mockRestClient.ExecuteAsync(Arg.Any<RestRequest>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(mockResponse));

        var mockRestClientFactory = Substitute.For<IRestClientFactory>();
        mockRestClientFactory.CreateClient(Arg.Any<RestClientOptions>())
            .Returns(mockRestClient);

        // 建立 LMStudioClient
        var options = Microsoft.Extensions.Options.Options.Create(MockData.CreateLMStudioOptions());
        var logger = Substitute.For<ILogger<LMStudioClient>>();
        var client = new LMStudioClient(options, logger, mockRestClientFactory);

        // Act
        var result = await client.SendTextRequestAsync(prompt);

        // Assert
        result.Should().Be("這是分析結果");
    }

    [Fact(Skip = "RestSharp 的 IsSuccessful 屬性無法在單元測試中正確模擬，需要重構為整合測試")]
    public async Task SendTextRequestAsync_WithEmptyPrompt_ShouldHandleGracefully()
    {
        // Arrange
        var emptyPrompt = "";
        var expectedResponse = """
        {
            "choices": [
                {
                    "message": {
                        "content": ""
                    }
                }
            ]
        }
        """;

        // 使用 TestFixtures 創建成功的 RestResponse
        var mockResponse = TestFixtures.CreateSuccessfulRestResponse(expectedResponse);

        // 創建 mock RestClient 和 RestClientFactory
        var mockRestClient = Substitute.For<global::VideoContentAnalyzer.Infrastructure.Http.IRestClient>();
        mockRestClient.ExecuteAsync(Arg.Any<RestRequest>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(mockResponse));

        var mockRestClientFactory = Substitute.For<IRestClientFactory>();
        mockRestClientFactory.CreateClient(Arg.Any<RestClientOptions>())
            .Returns(mockRestClient);

        // 建立 LMStudioClient
        var options = Microsoft.Extensions.Options.Options.Create(MockData.CreateLMStudioOptions());
        var logger = Substitute.For<ILogger<LMStudioClient>>();
        var client = new LMStudioClient(options, logger, mockRestClientFactory);

        // Act
        var result = await client.SendTextRequestAsync(emptyPrompt);

        // Assert
        result.Should().NotBeNull();
        result.Should().Be("");
    }

    [Fact(Skip = "RestSharp 的 IsSuccessful 屬性無法在單元測試中正確模擬，需要重構為整合測試")]
    public async Task SendTextRequestAsync_WithHttpError_ShouldThrowHttpRequestException()
    {
        // Arrange
        var prompt = "測試提示";

        // 使用 TestFixtures 創建失敗的 RestResponse
        var mockResponse = TestFixtures.CreateFailedRestResponse(HttpStatusCode.BadRequest, "Bad Request");

        // 創建 mock RestClient 和 RestClientFactory
        var mockRestClient = Substitute.For<global::VideoContentAnalyzer.Infrastructure.Http.IRestClient>();
        mockRestClient.ExecuteAsync(Arg.Any<RestRequest>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(mockResponse));

        var mockRestClientFactory = Substitute.For<IRestClientFactory>();
        mockRestClientFactory.CreateClient(Arg.Any<RestClientOptions>())
            .Returns(mockRestClient);

        // 建立 LMStudioClient
        var options = Microsoft.Extensions.Options.Options.Create(MockData.CreateLMStudioOptions());
        var logger = Substitute.For<ILogger<LMStudioClient>>();
        var client = new LMStudioClient(options, logger, mockRestClientFactory);

        // Act & Assert
        await Assert.ThrowsAsync<HttpRequestException>(
            () => client.SendTextRequestAsync(prompt));
    }

    [Fact()]
    public async Task SendTextRequestAsync_WithCancellationToken_ShouldRespectCancellation()
    {
        // Arrange
        var prompt = "測試提示";
        var cancellationToken = TestFixtures.CreateCancelledToken();

        // 模擬會被取消的 RestClient
        var mockRestClient = Substitute.For<global::VideoContentAnalyzer.Infrastructure.Http.IRestClient>();
        mockRestClient.ExecuteAsync(Arg.Any<RestRequest>(), Arg.Any<CancellationToken>())
            .Returns<RestResponse>(_ => throw new OperationCanceledException());

        var mockRestClientFactory = Substitute.For<IRestClientFactory>();
        mockRestClientFactory.CreateClient(Arg.Any<RestClientOptions>())
            .Returns(mockRestClient);

        // 建立 LMStudioClient
        var options = Microsoft.Extensions.Options.Options.Create(MockData.CreateLMStudioOptions());
        var logger = Substitute.For<ILogger<LMStudioClient>>();
        var client = new LMStudioClient(options, logger, mockRestClientFactory);

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => client.SendTextRequestAsync(prompt, cancellationToken));
    }

    #endregion

    #region ConvertImageToBase64Async Tests

    [Fact(Skip = "ConvertImageToBase64Async 是私有方法，需要檔案系統重構")]
    public async Task ConvertImageToBase64Async_WithValidImage_ShouldReturnBase64String()
    {
        // Arrange
        var imagePath = "/test/frame.jpg";
        var fileSystem = TestFixtures.CreateMockFileSystem();

        // Act & Assert
        // 實際測試需要重構 LMStudioClient 以支援檔案系統抽象
        await Task.CompletedTask;

        // 預期的斷言：
        // var result = await _client.ConvertImageToBase64Async(imagePath);
        // result.Should().NotBeNullOrEmpty();
        // result.Should().MatchRegex("^[A-Za-z0-9+/]*={0,2}$"); // Base64 格式驗證
    }

    [Fact(Skip = "ConvertImageToBase64Async 是私有方法，需要檔案系統重構")]
    public async Task ConvertImageToBase64Async_WithInvalidPath_ShouldThrowFileNotFoundException()
    {
        // Arrange
        var invalidPath = "/nonexistent/image.jpg";

        // Act & Assert
        // TODO: ConvertImageToBase64Async 是私有方法，無法直接測試
        // await Assert.ThrowsAsync<FileNotFoundException>(
        //     () => _client.ConvertImageToBase64Async(invalidPath));
        await Task.CompletedTask;
    }

    [Fact()]
    public async Task ConvertImageToBase64Async_WithLargeImage_ShouldHandleEfficiently()
    {
        // Arrange
        var imagePath = "/test/large_image.jpg";
        // 建立大型圖片檔案的模擬

        // Act & Assert
        // 測試大型檔案的處理效率
        await Task.CompletedTask;

        // 預期的斷言：
        // var stopwatch = Stopwatch.StartNew();
        // var result = await _client.ConvertImageToBase64Async(imagePath);
        // stopwatch.Stop();
        //
        // result.Should().NotBeNullOrEmpty();
        // stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000); // 5秒內完成
    }

    #endregion

    #region Dispose Tests

    [Fact()]
    public void Dispose_ShouldDisposeResourcesProperly()
    {
        // Arrange & Act
        _client.Dispose();

        // Assert
        // 驗證資源已正確釋放
        // 實際測試需要檢查 RestClient 是否已被釋放
    }

    [Fact()]
    public void Dispose_CalledMultipleTimes_ShouldNotThrow()
    {
        // Arrange & Act & Assert
        _client.Dispose();
        _client.Dispose(); // 第二次呼叫不應拋出例外
    }

    #endregion

    public void Dispose()
    {
        _client?.Dispose();
    }
}
