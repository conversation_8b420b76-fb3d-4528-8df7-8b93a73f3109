using RestSharp;
using System.Net;

// 測試 RestResponse 的 IsSuccessful 屬性
var request = new RestRequest();

// 測試我們的 mock 設定
var response = new RestResponse(request)
{
    StatusCode = HttpStatusCode.OK,
    Content = "test content",
    ResponseStatus = ResponseStatus.Completed,
    ErrorMessage = null
};

Console.WriteLine($"StatusCode: {response.StatusCode}");
Console.WriteLine($"ResponseStatus: {response.ResponseStatus}");
Console.WriteLine($"IsSuccessful: {response.IsSuccessful}");
Console.WriteLine($"ErrorMessage: {response.ErrorMessage}");
Console.WriteLine($"Content: {response.Content}");
