# Video Content Analyzer Configuration

Logging:
  LogLevel:
    Default: Information
    Microsoft: Warning
    Microsoft.Hosting.Lifetime: Information

# LM Studio Configuration
LMStudio:
  BaseUrl: http://*********:1234
  VisionModel: google/gemma-3-12b
  TextModel: google/gemma-3-12b
  MaxTokens: 5000
  Temperature: 0.7
  TimeoutSeconds: 300
  MaxImageSize: 1024
  ImageQuality: 85

# Whisper Configuration
Whisper:
  ModelPath: /Users/<USER>/.whisper/ggml-base.bin
  ModelSize: base
  Language: auto
  Translate: false
  Threads: 4

# Frame Extraction Configuration
FrameExtraction:
  # 截圖儲存路徑設定 (可使用相對路徑或絕對路徑)
  OutputDirectory: ./data/frames   # 儲存到統一的 data/frames 資料夾
  # OutputDirectory: ~/VideoAnalysis/frames  # 儲存到使用者目錄
  # OutputDirectory: /tmp/video-frames       # 儲存到系統臨時目錄
  
  # 是否使用時間戳命名
  UseTimestampInFilename: true
  
  # 截圖檔案格式
  ImageFormat: jpg
  
  # 圖片品質 (1-100, JPEG 格式適用)
  ImageQuality: 90
  
  # 是否保留截圖檔案 (false 表示分析完成後自動清理)
  KeepFrames: false
  
  # 是否為每個影片建立獨立子目錄
  CreateSubDirectoryPerVideo: true
  
  # 子目錄命名格式 (支援 {videoName}, {timestamp} 等變數)
  SubDirectoryNameFormat: "{videoName}_{timestamp:yyyyMMdd_HHmmss}"

# 場所識別設定
PlaceRecognition:
  # 是否啟用餐廳/景點識別模式
  EnablePlaceInfoExtraction: true
  
  # 目標語言 (影響文字識別重點)
  TargetLanguages: 
    - "zh-TW"  # 繁體中文
    - "ja"     # 日文
    - "ko"     # 韓文
    - "zh-CN"  # 簡體中文
  
  # 場所資訊信心度閾值 (0-1)
  PlaceInfoConfidenceThreshold: 0.85
  
  # 文字識別信心度閾值 (0-1) 
  TextConfidenceThreshold: 0.85
  
  # 是否在摘要中優先顯示地點資訊
  PrioritizePlaceInfoInSummary: true
  
  # 場所類型優先級 (影響識別重點)
  PreferredCategories:
    - "restaurant"  # 餐廳
    - "cafe"        # 咖啡廳
    - "attraction"  # 景點
    - "hotel"       # 飯店
    - "shop"        # 商店

# YouTube API 設定
YouTubeApi:
  # API 金鑰 (優先從環境變數 YOUTUBE_API_KEY 讀取)
  ApiKey: "AIzaSyChwHQ7Lg5mSNUPgKbIEHAlH1AXzZuwSJE"
  
  # 應用程式名稱
  ApplicationName: "VideoContentAnalyzer"
  
  # 預設地區代碼 (用於分類資訊)
  DefaultRegionCode: "TW"
  
  # 偏好的字幕語言順序 (根據影片語言自動選擇)
  PreferredSubtitleLanguages:
    - "zh-TW"    # 繁體中文
    - "zh-CN"    # 簡體中文
    - "ja"       # 日文
    - "ko"       # 韓文
    - "en"       # 英文

# Google Places API 設定
GooglePlaces:
  # API 金鑰 (優先從環境變數 GooglePlaces__ApiKey 讀取)
  ApiKey: ""
  
  # 搜尋半徑 (公尺)
  SearchRadius: 5000
  
  # 搜尋語言
  Language: "zh-TW"
  
  # 地區代碼
  RegionCode: "TW"
  
  # 是否包含價格等級資訊
  IncludePriceLevel: true
  
  # 是否包含營業時間資訊
  IncludeOpeningHours: true
  
  # 是否包含評論資訊
  IncludeReviews: false
  
  # 搜尋類型優先級
  PreferredTypes:
    - "restaurant"
    - "tourist_attraction"
    - "lodging"
    - "point_of_interest"
    - "establishment"
  
  # API 請求超時設定（秒）
  TimeoutSeconds: 30
  
  # 是否啟用地點檢測 (預設關閉，只在旅遊/美食影片時啟用)
  
  # 自動檢測閾值設定
  Detection:
    # 信心度閾值 (0-1)
    ConfidenceThreshold: 0.9
    # 每個來源的最大檢測地點數
    MaxPlacesPerSource: 10
    # 總檢測地點數上限
    MaxTotalPlaces: 30
  
  # 地點驗證設定
  Validation:
    # 是否啟用地點相關性驗證
    EnableRelevanceCheck: true
    
    # 是否啟用分店判斷（當有多個結果時）
    EnableBranchDetection: true
    
    # 相關性信心度門檻 (0-1)
    RelevanceThreshold: 0.7
    
    # 分店相似度門檻 (0-1)
    BranchSimilarityThreshold: 0.8
    
    # 最大驗證嘗試次數
    MaxValidationAttempts: 3

# YouTube 下載設定
YouTubeDownload:
  # 下載檔案儲存目錄
  OutputDirectory: ./data/downloads
  
  # 偏好的影片品質 (720p, 1080p, best, worst)
  PreferredQuality: 720p
  
  # 偏好的影片格式 (mp4, webm, mkv)
  PreferredFormat: mp4
  
  # 是否保持原始檔案名稱
  KeepOriginalFilename: false
  
  # 檔案名稱模板 (使用 yt-dlp 格式)
  FilenameTemplate: "%(title)s.%(ext)s"
  
  # 是否下載字幕
  DownloadSubtitles: true
  
  # 字幕語言設定 (中文、英文、日文、韓文) - 備用設定
  SubtitleLanguages: "zh,en,ja,ko"
  
  # 是否將字幕嵌入影片
  EmbedSubtitles: false
  
  # 下載超時設定（秒）
  TimeoutSeconds: 300
  
  # 分析完成後是否自動清理下載檔案
  CleanupAfterAnalysis: true

# OpenRouter API 設定
OpenRouter:
  # API 基本設定
  BaseUrl: "https://openrouter.ai/api/v1"
  
  # 用於餐廳名稱提取的模型
  RestaurantExtractionModel: "deepseek/deepseek-r1-0528-qwen3-8b:free"
  
  # 兩把 API Key (優先從環境變數讀取)
  ApiKeyA: "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"  # 從 OPENROUTER_API_KEY_A 環境變數讀取
  ApiKeyB: "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"  # 從 OPENROUTER_API_KEY_B 環境變數讀取
  
  # API 請求設定
  MaxTokens: 2000
  Temperature: 0.3  # 較低溫度確保更一致的提取結果
  TimeoutSeconds: 60
  
  # 請求重試設定
  MaxRetryAttempts: 3
  RetryDelaySeconds: 1

# 進階設定
Advanced:
  # 最大並行處理數
  MaxConcurrentFrameAnalysis: 3
  
  # 分析結果快取設定
  EnableResultCache: true
  CacheDirectory: ./data/cache
  
  # 錯誤重試設定
  MaxRetryAttempts: 3
  RetryDelaySeconds: 2