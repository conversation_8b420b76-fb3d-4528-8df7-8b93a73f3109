using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Diagnostics;
using System.Text;
using System.Text.Json;
using VideoContentAnalyzer.Core.Interfaces;
using VideoContentAnalyzer.Core.Models;

namespace VideoContentAnalyzer.Infrastructure.Places;

public class PlaceValidationService : IPlaceValidationService
{
    private readonly ILMStudioClient _lmStudioClient;
    private readonly GooglePlacesValidationOptions _options;
    private readonly ILogger<PlaceValidationService> _logger;

    public PlaceValidationService(
        ILMStudioClient lmStudioClient,
        IOptions<GooglePlacesOptions> options,
        ILogger<PlaceValidationService> logger)
    {
        _lmStudioClient = lmStudioClient;
        _options = options.Value.Validation;
        _logger = logger;
    }

    public bool IsRelevanceValidationEnabled => _options.EnableRelevanceCheck;
    public bool IsBranchDetectionEnabled => _options.EnableBranchDetection;

    public async Task<PlaceValidationResult> ValidatePlaceRelevanceAsync(
        PlaceReference place, 
        string videoSummary, 
        string videoTitle, 
        YouTubeVideoInfo? videoInfo = null, 
        CancellationToken cancellationToken = default)
    {
        if (!IsRelevanceValidationEnabled)
        {
            return new PlaceValidationResult
            {
                Place = place,
                IsRelevant = true,
                ConfidenceScore = 1.0,
                ValidationReason = "驗證功能已停用，預設為相關"
            };
        }

        var stopwatch = Stopwatch.StartNew();
        _logger.LogInformation("開始驗證地點相關性: {PlaceName}", place.Name);

        try
        {
            var prompt = BuildRelevanceValidationPrompt(place, videoSummary, videoTitle, videoInfo);
            
            for (int attempt = 1; attempt <= _options.MaxValidationAttempts; attempt++)
            {
                try
                {
                    var response = await _lmStudioClient.SendTextRequestAsync(prompt, cancellationToken);
                    var result = ParseRelevanceValidationResponse(response, place);
                    
                    if (result.ConfidenceScore >= _options.RelevanceThreshold)
                    {
                        _logger.LogInformation("地點驗證完成: {PlaceName} - 相關性: {IsRelevant} ({ConfidenceScore:F2})", 
                            place.Name, result.IsRelevant, result.ConfidenceScore);
                        return result;
                    }
                    
                    if (attempt < _options.MaxValidationAttempts)
                    {
                        _logger.LogWarning("地點驗證信心度不足 ({ConfidenceScore:F2} < {Threshold:F2}), 重試第 {Attempt} 次", 
                            result.ConfidenceScore, _options.RelevanceThreshold, attempt + 1);
                        await Task.Delay(1000 * attempt, cancellationToken);
                    }
                    else
                    {
                        return result;
                    }
                }
                catch (Exception ex) when (attempt < _options.MaxValidationAttempts)
                {
                    _logger.LogWarning(ex, "地點驗證失敗，重試第 {Attempt} 次", attempt + 1);
                    await Task.Delay(1000 * attempt, cancellationToken);
                }
            }

            return new PlaceValidationResult
            {
                Place = place,
                IsRelevant = false,
                ConfidenceScore = 0.0,
                ErrorMessage = "所有驗證嘗試均失敗"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "地點驗證過程中發生錯誤: {PlaceName}", place.Name);
            return new PlaceValidationResult
            {
                Place = place,
                IsRelevant = false,
                ConfidenceScore = 0.0,
                ErrorMessage = $"驗證失敗: {ex.Message}"
            };
        }
        finally
        {
            stopwatch.Stop();
            _logger.LogDebug("地點驗證耗時: {Duration:F2}ms", stopwatch.Elapsed.TotalMilliseconds);
        }
    }

    public async Task<BranchDetectionResult> DetectBranchesAsync(
        List<PlaceReference> places, 
        CancellationToken cancellationToken = default)
    {
        if (!IsBranchDetectionEnabled || places.Count < 2)
        {
            return new BranchDetectionResult
            {
                IndependentPlaces = places,
                TotalPlacesAnalyzed = places.Count
            };
        }

        var stopwatch = Stopwatch.StartNew();
        _logger.LogInformation("開始分店檢測，共 {PlaceCount} 個地點", places.Count);

        try
        {
            var prompt = BuildBranchDetectionPrompt(places);
            var response = await _lmStudioClient.SendTextRequestAsync(prompt, cancellationToken);
            var result = ParseBranchDetectionResponse(response, places);
            
            _logger.LogInformation("分店檢測完成: {BranchGroups} 個品牌群組, {IndependentPlaces} 個獨立地點", 
                result.BranchGroups.Count, result.IndependentPlaces.Count);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分店檢測過程中發生錯誤");
            return new BranchDetectionResult
            {
                IndependentPlaces = places,
                TotalPlacesAnalyzed = places.Count,
                ErrorMessage = $"分店檢測失敗: {ex.Message}"
            };
        }
        finally
        {
            stopwatch.Stop();
            _logger.LogDebug("分店檢測耗時: {Duration:F2}ms", stopwatch.Elapsed.TotalMilliseconds);
        }
    }

    public async Task<PlaceReference?> SelectMostRelevantBranchAsync(
        List<PlaceReference> branches, 
        string videoSummary, 
        string videoTitle, 
        YouTubeVideoInfo? videoInfo = null, 
        CancellationToken cancellationToken = default)
    {
        if (branches.Count <= 1)
            return branches.FirstOrDefault();

        _logger.LogInformation("從 {BranchCount} 個分店中選擇最相關的地點", branches.Count);

        try
        {
            var prompt = BuildBranchSelectionPrompt(branches, videoSummary, videoTitle, videoInfo);
            var response = await _lmStudioClient.SendTextRequestAsync(prompt, cancellationToken);
            var selectedBranch = ParseBranchSelectionResponse(response, branches);
            
            _logger.LogInformation("已選擇最相關分店: {SelectedBranch}", selectedBranch?.Name ?? "無");
            return selectedBranch;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分店選擇過程中發生錯誤");
            // 預設返回第一個分店
            return branches.FirstOrDefault();
        }
    }

    public async Task<List<PlaceValidationResult>> ValidatePlacesBatchAsync(
        List<PlaceReference> places, 
        string videoSummary, 
        string videoTitle, 
        YouTubeVideoInfo? videoInfo = null, 
        CancellationToken cancellationToken = default)
    {
        var results = new List<PlaceValidationResult>();
        
        _logger.LogInformation("開始批次驗證 {PlaceCount} 個地點", places.Count);

        var semaphore = new SemaphoreSlim(3); // 限制並發數
        var tasks = places.Select(async place =>
        {
            await semaphore.WaitAsync(cancellationToken);
            try
            {
                return await ValidatePlaceRelevanceAsync(place, videoSummary, videoTitle, videoInfo, cancellationToken);
            }
            finally
            {
                semaphore.Release();
            }
        });

        results.AddRange(await Task.WhenAll(tasks));
        
        _logger.LogInformation("批次驗證完成: {ValidPlaces}/{TotalPlaces} 個地點通過驗證", 
            results.Count(r => r.IsRelevant), results.Count);
        
        return results;
    }

    public async Task<ComprehensiveValidationResult> ValidateAndFilterPlacesAsync(
        List<PlaceReference> places, 
        string videoSummary, 
        string videoTitle, 
        YouTubeVideoInfo? videoInfo = null, 
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new ComprehensiveValidationResult
        {
            OriginalPlaceCount = places.Count
        };

        _logger.LogInformation("開始綜合地點驗證: {PlaceCount} 個地點", places.Count);

        try
        {
            // Step 1: 批次驗證地點相關性
            var validationResults = await ValidatePlacesBatchAsync(places, videoSummary, videoTitle, videoInfo, cancellationToken);
            result.ValidationResults = validationResults;

            // Step 2: 篩選出相關的地點
            var relevantPlaces = validationResults
                .Where(r => r.IsRelevant && r.ValidationSuccessful)
                .Select(r => r.Place)
                .ToList();

            result.FilteredOutPlaces = validationResults
                .Where(r => !r.IsRelevant || !r.ValidationSuccessful)
                .Select(r => r.Place)
                .ToList();

            // Step 3: 對相關地點進行分店檢測
            if (relevantPlaces.Count > 1)
            {
                result.BranchDetection = await DetectBranchesAsync(relevantPlaces, cancellationToken);
                
                // Step 4: 處理分店群組，為每個群組選擇最相關的分店
                var finalPlaces = new List<PlaceReference>();
                
                // 添加獨立地點
                finalPlaces.AddRange(result.BranchDetection.IndependentPlaces);
                
                // 處理分店群組
                foreach (var group in result.BranchDetection.BranchGroups)
                {
                    var selectedBranch = await SelectMostRelevantBranchAsync(
                        group.Branches, videoSummary, videoTitle, videoInfo, cancellationToken);
                    
                    if (selectedBranch != null)
                    {
                        group.MostRelevantBranch = selectedBranch;
                        finalPlaces.Add(selectedBranch);
                    }
                }
                
                result.ValidatedPlaces = finalPlaces;
            }
            else
            {
                result.ValidatedPlaces = relevantPlaces;
            }

            result.FinalPlaceCount = result.ValidatedPlaces.Count;
            result.ValidationDuration = stopwatch.Elapsed;

            _logger.LogInformation("綜合驗證完成: {Original} -> {Final} 個地點 ({Duration:F2}s)", 
                result.OriginalPlaceCount, result.FinalPlaceCount, stopwatch.Elapsed.TotalSeconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "綜合驗證過程中發生錯誤");
            result.ErrorMessage = $"驗證失敗: {ex.Message}";
            result.ValidationDuration = stopwatch.Elapsed;
            return result;
        }
    }

    #region Private Methods

    private string BuildRelevanceValidationPrompt(PlaceReference place, string videoSummary, string videoTitle, YouTubeVideoInfo? videoInfo)
    {
        var prompt = new StringBuilder();
        prompt.AppendLine("你是一個專業的地點相關性驗證專家。請分析以下地點是否與影片內容相關。");
        prompt.AppendLine();
        prompt.AppendLine("=== 影片資訊 ===");
        prompt.AppendLine($"標題: {videoTitle}");
        prompt.AppendLine($"摘要: {videoSummary}");
        
        if (videoInfo != null)
        {
            if (!string.IsNullOrEmpty(videoInfo.Description))
                prompt.AppendLine($"描述: {videoInfo.Description}");
            if (videoInfo.Tags.Any())
                prompt.AppendLine($"標籤: {string.Join(", ", videoInfo.Tags)}");
            if (!string.IsNullOrEmpty(videoInfo.Channel))
                prompt.AppendLine($"頻道: {videoInfo.Channel}");
        }
        
        prompt.AppendLine();
        prompt.AppendLine("=== 地點資訊 ===");
        prompt.AppendLine($"名稱: {place.Name}");
        if (!string.IsNullOrEmpty(place.OriginalQueryName))
            prompt.AppendLine($"原始查詢名稱: {place.OriginalQueryName}");
        if (!string.IsNullOrEmpty(place.FormattedAddress))
            prompt.AppendLine($"地址: {place.FormattedAddress}");
        if (place.Types.Any())
            prompt.AppendLine($"類型: {string.Join(", ", place.Types)}");
        if (place.Rating.HasValue)
            prompt.AppendLine($"評分: {place.Rating:F1}");
        if (!string.IsNullOrEmpty(place.DetectionSource))
            prompt.AppendLine($"檢測來源: {place.DetectionSource}");
        
        prompt.AppendLine();
        prompt.AppendLine("請基於以上資訊，評估這個地點是否與影片內容相關。");
        prompt.AppendLine("回應格式(JSON):");
        prompt.AppendLine(@"{
  ""isRelevant"": true/false,
  ""confidenceScore"": 0.0-1.0,
  ""reason"": ""詳細解釋為什麼相關或不相關""
}");
        
        return prompt.ToString();
    }

    private string BuildBranchDetectionPrompt(List<PlaceReference> places)
    {
        var prompt = new StringBuilder();
        prompt.AppendLine("你是一個專業的連鎖店分店檢測專家。請分析以下地點是否屬於同一品牌的不同分店。");
        prompt.AppendLine();
        prompt.AppendLine("=== 地點清單 ===");
        
        for (int i = 0; i < places.Count; i++)
        {
            var place = places[i];
            prompt.AppendLine($"地點 {i + 1}:");
            prompt.AppendLine($"  名稱: {place.Name}");
            if (!string.IsNullOrEmpty(place.OriginalQueryName))
                prompt.AppendLine($"  原始名稱: {place.OriginalQueryName}");
            if (!string.IsNullOrEmpty(place.FormattedAddress))
                prompt.AppendLine($"  地址: {place.FormattedAddress}");
            if (place.Types.Any())
                prompt.AppendLine($"  類型: {string.Join(", ", place.Types)}");
            prompt.AppendLine();
        }
        
        prompt.AppendLine("請識別哪些地點屬於同一品牌的分店，並按品牌分組。");
        prompt.AppendLine("回應格式(JSON):");
        prompt.AppendLine(@"{
  ""branchGroups"": [
    {
      ""brandName"": ""品牌名稱"",
      ""branches"": [1, 3, 5],
      ""similarityScore"": 0.9,
      ""reason"": ""分組原因""
    }
  ],
  ""independentPlaces"": [2, 4]
}");
        
        return prompt.ToString();
    }

    private string BuildBranchSelectionPrompt(List<PlaceReference> branches, string videoSummary, string videoTitle, YouTubeVideoInfo? videoInfo)
    {
        var prompt = new StringBuilder();
        prompt.AppendLine("你是一個專業的地點選擇專家。請從以下同品牌的分店中選擇最符合影片內容的地點。");
        prompt.AppendLine();
        prompt.AppendLine("=== 影片資訊 ===");
        prompt.AppendLine($"標題: {videoTitle}");
        prompt.AppendLine($"摘要: {videoSummary}");
        
        if (videoInfo != null && !string.IsNullOrEmpty(videoInfo.Description))
            prompt.AppendLine($"描述: {videoInfo.Description}");
        
        prompt.AppendLine();
        prompt.AppendLine("=== 分店選項 ===");
        
        for (int i = 0; i < branches.Count; i++)
        {
            var branch = branches[i];
            prompt.AppendLine($"選項 {i + 1}:");
            prompt.AppendLine($"  名稱: {branch.Name}");
            if (!string.IsNullOrEmpty(branch.FormattedAddress))
                prompt.AppendLine($"  地址: {branch.FormattedAddress}");
            if (branch.Rating.HasValue)
                prompt.AppendLine($"  評分: {branch.Rating:F1}");
            prompt.AppendLine();
        }
        
        prompt.AppendLine("請選擇最符合影片內容的分店。");
        prompt.AppendLine("回應格式(JSON):");
        prompt.AppendLine(@"{
  ""selectedIndex"": 1,
  ""reason"": ""選擇原因""
}");
        
        return prompt.ToString();
    }

    private PlaceValidationResult ParseRelevanceValidationResponse(string response, PlaceReference place)
    {
        try
        {
            var json = JsonDocument.Parse(response);
            var root = json.RootElement;
            
            return new PlaceValidationResult
            {
                Place = place,
                IsRelevant = root.GetProperty("isRelevant").GetBoolean(),
                ConfidenceScore = root.GetProperty("confidenceScore").GetDouble(),
                ValidationReason = root.GetProperty("reason").GetString()
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "無法解析相關性驗證回應，使用預設值: {Response}", response);
            return new PlaceValidationResult
            {
                Place = place,
                IsRelevant = false,
                ConfidenceScore = 0.0,
                ValidationReason = "回應解析失敗",
                ErrorMessage = $"回應解析錯誤: {ex.Message}"
            };
        }
    }

    private BranchDetectionResult ParseBranchDetectionResponse(string response, List<PlaceReference> originalPlaces)
    {
        try
        {
            var json = JsonDocument.Parse(response);
            var root = json.RootElement;
            
            var result = new BranchDetectionResult
            {
                TotalPlacesAnalyzed = originalPlaces.Count
            };
            
            // 解析分店群組
            if (root.TryGetProperty("branchGroups", out var branchGroupsElement))
            {
                foreach (var groupElement in branchGroupsElement.EnumerateArray())
                {
                    var group = new BranchGroup
                    {
                        BrandName = groupElement.GetProperty("brandName").GetString()!,
                        SimilarityScore = groupElement.GetProperty("similarityScore").GetDouble(),
                        GroupingReason = groupElement.TryGetProperty("reason", out var reasonElement) 
                            ? reasonElement.GetString() : null
                    };
                    
                    if (groupElement.TryGetProperty("branches", out var branchesElement))
                    {
                        foreach (var indexElement in branchesElement.EnumerateArray())
                        {
                            var index = indexElement.GetInt32() - 1; // 轉換為 0-based index
                            if (index >= 0 && index < originalPlaces.Count)
                            {
                                group.Branches.Add(originalPlaces[index]);
                            }
                        }
                    }
                    
                    result.BranchGroups.Add(group);
                }
            }
            
            // 解析獨立地點
            if (root.TryGetProperty("independentPlaces", out var independentElement))
            {
                foreach (var indexElement in independentElement.EnumerateArray())
                {
                    var index = indexElement.GetInt32() - 1; // 轉換為 0-based index
                    if (index >= 0 && index < originalPlaces.Count)
                    {
                        result.IndependentPlaces.Add(originalPlaces[index]);
                    }
                }
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "無法解析分店檢測回應，將所有地點標記為獨立: {Response}", response);
            return new BranchDetectionResult
            {
                IndependentPlaces = originalPlaces,
                TotalPlacesAnalyzed = originalPlaces.Count,
                ErrorMessage = $"回應解析錯誤: {ex.Message}"
            };
        }
    }

    private PlaceReference? ParseBranchSelectionResponse(string response, List<PlaceReference> branches)
    {
        try
        {
            var json = JsonDocument.Parse(response);
            var root = json.RootElement;
            
            var selectedIndex = root.GetProperty("selectedIndex").GetInt32() - 1; // 轉換為 0-based index
            
            if (selectedIndex >= 0 && selectedIndex < branches.Count)
            {
                return branches[selectedIndex];
            }
            
            _logger.LogWarning("選擇的分店索引超出範圍: {SelectedIndex}, 總數: {BranchCount}", 
                selectedIndex + 1, branches.Count);
            return branches.FirstOrDefault();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "無法解析分店選擇回應，返回第一個分店: {Response}", response);
            return branches.FirstOrDefault();
        }
    }

    #endregion
}