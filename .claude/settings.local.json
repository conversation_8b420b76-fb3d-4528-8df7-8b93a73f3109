{"permissions": {"allow": ["Bash(dotnet new:*)", "Bash(dotnet sln:*)", "Bash(dotnet restore:*)", "Bash(dotnet build)", "<PERSON><PERSON>(python3:*)", "Bash(pip3 install:*)", "<PERSON><PERSON>(curl:*)", "Bash(ping:*)", "Bash(dotnet run:*)", "Bash(dotnet build:*)", "Bash(dotnet add:*)", "Bash(timeout 30 dotnet run --project src/VideoContentAnalyzer.Console -- --youtube \"https://www.youtube.com/watch?v=feT2fQ7hR-I\" --verbose --max-frames 1)", "Bash(YOUTUBE_API_KEY=AIzaSyChwHQ7Lg5mSNUPgKbIEHAlH1AXzZuwSJE dotnet run --project src/VideoContentAnalyzer.Console -- --youtube \"https://www.youtube.com/watch?v=feT2fQ7hR-I\" --output json --verbose --no-subtitles --interval 60 --max-frames 1)", "Bash(timeout 120 dotnet run --project src/VideoContentAnalyzer.Console -- --youtube \"https://www.youtube.com/watch?v=feT2fQ7hR-I\" --verbose --max-frames 1 --no-subtitles)", "Bash(dotnet clean:*)", "<PERSON><PERSON>(whisper:*)", "Bash(ffmpeg:*)", "<PERSON><PERSON>(timeout 60 dotnet run:*)", "Bash(timeout 300 dotnet run --project src/VideoContentAnalyzer.Console -- \"./src/VideoContentAnalyzer.Console/downloads/feT2fQ7hR-I.mp4\" --verbose --max-frames 1 --interval 60)", "<PERSON><PERSON>(timeout:*)", "mcp__serena__find_symbol", "mcp__serena__search_for_pattern", "<PERSON><PERSON>(echo:*)", "mcp__serena__replace_symbol_body", "mcp__serena__get_symbols_overview", "mcp__serena__insert_before_symbol", "mcp__serena__check_onboarding_performed", "mcp__serena__onboarding", "mcp__serena__list_dir", "mcp__serena__write_memory", "mcp__serena__think_about_collected_information", "mcp__serena__insert_after_symbol", "<PERSON><PERSON>(cat:*)", "Read(//Users/<USER>/RiderProjects/yt-dlc/**)", "mcp__serena__find_file", "Bash(yt-dlp:*)", "Bash(grep:*)", "<PERSON><PERSON>(env)", "mcp__serena__find_referencing_symbols", "<PERSON><PERSON>(chmod:*)", "Bash(./test-google-places.sh:*)", "Read(//Users/<USER>/**)", "mcp__filesystem__read_text_file", "Bash(dotnet test:*)", "mcp__filesystem__write_file", "<PERSON><PERSON>(csc:*)", "Bash(./test-log-path.exe)", "Bash(/Users/<USER>/RiderProjects/video-content-analyze/test-log-path.exe)"], "deny": [], "ask": [], "additionalDirectories": ["/Users/<USER>/.whisper", "/private/var/folders/m4/x0lfbl2s03j0g78vwlwxbp940000gn/T/VideoContentAnalyzer/Audio", "/private/tmp"]}, "enabledMcpjsonServers": ["github", "webresearch", "filesystem", "think", "desktop-commander", "playwright", "Context7", "brave-search", "grafana", "kubernetes", "taskmaster-ai", "postgres", "mssql", "serena"]}